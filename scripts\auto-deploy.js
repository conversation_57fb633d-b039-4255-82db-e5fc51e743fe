#!/usr/bin/env node

/**
 * Automated Deployment Script for Inno-CRM
 * 
 * This script handles complete automated deployment to Vercel:
 * 1. Validates environment and dependencies
 * 2. Fixes common deployment issues
 * 3. Optimizes configuration
 * 4. Deploys to Vercel
 * 5. Verifies deployment success
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Polyfill fetch for Node.js versions that don't have it
if (typeof fetch === 'undefined') {
  global.fetch = require('node-fetch');
}

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m'
};

function log(message, color = 'white') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logStep(step, message) {
  log(`\n🚀 STEP ${step}: ${message}`, 'cyan');
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function execCommand(command, description) {
  try {
    log(`   Running: ${command}`, 'blue');
    const result = execSync(command, { encoding: 'utf8', stdio: 'pipe' });
    logSuccess(`${description} completed`);
    return result;
  } catch (error) {
    logError(`${description} failed: ${error.message}`);
    throw error;
  }
}

class AutoDeployer {
  constructor() {
    this.projectRoot = process.cwd();
    this.serverType = process.env.SERVER_TYPE || 'staff';
    this.isStaffServer = this.serverType === 'staff';
    this.deploymentConfig = {};
  }

  async deploy() {
    try {
      log('\n🎯 AUTOMATED DEPLOYMENT STARTING', 'magenta');
      log('=====================================', 'magenta');
      
      await this.step1_ValidateEnvironment();
      await this.step2_FixDependencies();
      await this.step3_OptimizeConfiguration();
      await this.step4_SetupDatabase();
      await this.step5_ConfigureVercel();
      await this.step6_Deploy();
      await this.step7_Verify();
      
      log('\n🎉 DEPLOYMENT COMPLETED SUCCESSFULLY!', 'green');
      log('=====================================', 'green');
      
    } catch (error) {
      logError(`Deployment failed: ${error.message}`);
      process.exit(1);
    }
  }

  async step1_ValidateEnvironment() {
    logStep(1, 'Validating Environment');
    
    // Check Node.js version
    const nodeVersion = process.version;
    log(`   Node.js version: ${nodeVersion}`);
    
    // Check if we're in the right directory
    if (!fs.existsSync('package.json')) {
      throw new Error('package.json not found. Run this script from the project root.');
    }
    
    // Check required files
    const requiredFiles = [
      'package.json',
      'next.config.js',
      'prisma/schema.prisma',
      'lib/auth.ts',
      'middleware.ts'
    ];
    
    for (const file of requiredFiles) {
      if (fs.existsSync(file)) {
        logSuccess(`Found ${file}`);
      } else {
        throw new Error(`Required file missing: ${file}`);
      }
    }
    
    // Check if Vercel CLI is installed
    try {
      execSync('vercel --version', { stdio: 'pipe' });
      logSuccess('Vercel CLI is installed');
    } catch (error) {
      logWarning('Vercel CLI not found. Installing...');
      execCommand('npm install -g vercel', 'Vercel CLI installation');
    }
  }

  async step2_FixDependencies() {
    logStep(2, 'Fixing Dependencies and Build Configuration');
    
    // Clean install dependencies
    if (fs.existsSync('node_modules')) {
      log('   Cleaning node_modules...');
      execCommand('rm -rf node_modules package-lock.json', 'Clean dependencies');
    }
    
    execCommand('npm install', 'Install dependencies');
    
    // Verify critical dependencies
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    const criticalDeps = [
      '@prisma/client',
      'prisma',
      'next-auth',
      'bcryptjs',
      'zod',
      'next'
    ];
    
    for (const dep of criticalDeps) {
      if (packageJson.dependencies[dep] || packageJson.devDependencies[dep]) {
        logSuccess(`${dep} is installed`);
      } else {
        logWarning(`Installing missing dependency: ${dep}`);
        execCommand(`npm install ${dep}`, `Install ${dep}`);
      }
    }
    
    // Generate Prisma client
    execCommand('npx prisma generate', 'Generate Prisma client');
  }

  async step3_OptimizeConfiguration() {
    logStep(3, 'Optimizing Configuration Files');
    
    await this.optimizeVercelConfig();
    await this.optimizeNextConfig();
    await this.optimizePrismaConfig();
    await this.createEnvironmentTemplate();
  }

  async optimizeVercelConfig() {
    const vercelConfig = {
      "buildCommand": "prisma generate && next build",
      "installCommand": "npm install",
      "framework": "nextjs",
      "regions": ["iad1"],
      "functions": {
        "app/api/**/*.ts": {
          "maxDuration": 30
        }
      },
      "env": {
        "PRISMA_GENERATE_DATAPROXY": "true"
      },
      "build": {
        "env": {
          "PRISMA_GENERATE_DATAPROXY": "true"
        }
      },
      "headers": [
        {
          "source": "/api/inter-server/(.*)",
          "headers": [
            {
              "key": "Access-Control-Allow-Origin",
              "value": this.isStaffServer ? "https://inno-crm-admin.vercel.app" : "https://inno-crm-staff.vercel.app"
            },
            {
              "key": "Access-Control-Allow-Methods",
              "value": "GET, POST, PUT, DELETE, OPTIONS"
            },
            {
              "key": "Access-Control-Allow-Headers",
              "value": "Content-Type, Authorization, X-Inter-Server-Secret"
            },
            {
              "key": "Access-Control-Allow-Credentials",
              "value": "true"
            }
          ]
        },
        {
          "source": "/(.*)",
          "headers": [
            {
              "key": "X-Frame-Options",
              "value": "DENY"
            },
            {
              "key": "X-Content-Type-Options",
              "value": "nosniff"
            },
            {
              "key": "Referrer-Policy",
              "value": "strict-origin-when-cross-origin"
            },
            {
              "key": "Strict-Transport-Security",
              "value": "max-age=31536000; includeSubDomains"
            }
          ]
        }
      ]
    };
    
    fs.writeFileSync('vercel.json', JSON.stringify(vercelConfig, null, 2));
    logSuccess('Optimized vercel.json');
  }

  async optimizeNextConfig() {
    const nextConfig = `/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    domains: ['res.cloudinary.com'],
  },
  serverExternalPackages: ['@prisma/client', 'bcryptjs'],
  webpack: (config, { isServer }) => {
    if (isServer) {
      config.externals.push('@prisma/client')
    }
    return config
  },
  env: {
    PRISMA_GENERATE_DATAPROXY: process.env.PRISMA_GENERATE_DATAPROXY,
  },
  experimental: {
    serverComponentsExternalPackages: ['@prisma/client', 'bcryptjs']
  }
}

module.exports = nextConfig
`;
    
    fs.writeFileSync('next.config.js', nextConfig);
    logSuccess('Optimized next.config.js');
  }

  async optimizePrismaConfig() {
    // Ensure Prisma schema has correct configuration
    let schema = fs.readFileSync('prisma/schema.prisma', 'utf8');
    
    // Check if datasource is properly configured
    if (!schema.includes('provider = "postgresql"')) {
      logWarning('Prisma schema might not be configured for PostgreSQL');
    }
    
    logSuccess('Prisma configuration validated');
  }

  async createEnvironmentTemplate() {
    const envTemplate = `# PRODUCTION ENVIRONMENT VARIABLES
# Copy these to Vercel Dashboard → Settings → Environment Variables

# Database Configuration
DATABASE_URL="your-production-database-url"
PRISMA_GENERATE_DATAPROXY="true"

# Authentication
NEXTAUTH_SECRET="your-32-character-secret-key-here"
NEXTAUTH_URL="https://your-app.vercel.app"

# Application Configuration
APP_NAME="Innovative Centre${this.isStaffServer ? ' - Staff Portal' : ' - Admin Portal'}"
APP_URL="https://your-app.vercel.app"
APP_ENV="production"
SERVER_TYPE="${this.serverType}"

# Inter-Server Communication
ADMIN_SERVER_URL="https://inno-crm-admin.vercel.app"
STAFF_SERVER_URL="https://inno-crm-staff.vercel.app"
INTER_SERVER_SECRET="your-inter-server-secret-key"

# Optional: SMS/Email Configuration
SMS_PROVIDER="eskiz"
SMS_API_KEY="your-sms-api-key"
EMAIL_PROVIDER="gmail"
EMAIL_USER="<EMAIL>"
EMAIL_PASSWORD="your-app-password"
`;
    
    fs.writeFileSync('.env.production.template', envTemplate);
    logSuccess('Created environment template');
  }

  async step4_SetupDatabase() {
    logStep(4, 'Setting Up Database Configuration');

    // Validate DATABASE_URL format if provided
    const databaseUrl = process.env.DATABASE_URL;
    if (databaseUrl) {
      if (databaseUrl.startsWith('postgresql://') || databaseUrl.startsWith('postgres://')) {
        logSuccess('Database URL format is valid');
      } else {
        logWarning('Database URL format might be incorrect');
      }
    } else {
      logWarning('DATABASE_URL not set in environment');
    }

    // Test Prisma generation
    try {
      execCommand('npx prisma generate', 'Generate Prisma client');
    } catch (error) {
      logError('Prisma generation failed. Check your schema.');
      throw error;
    }
  }

  async step5_ConfigureVercel() {
    logStep(5, 'Configuring Vercel Project');

    // Login to Vercel (if not already logged in)
    try {
      execCommand('vercel whoami', 'Check Vercel authentication');
      logSuccess('Already logged in to Vercel');
    } catch (error) {
      log('   Please login to Vercel...');
      execCommand('vercel login', 'Login to Vercel');
    }

    // Create or link Vercel project
    try {
      if (fs.existsSync('.vercel')) {
        logSuccess('Vercel project already linked');
      } else {
        log('   Linking Vercel project...');
        execCommand('vercel link --yes', 'Link Vercel project');
      }
    } catch (error) {
      logWarning('Vercel project linking failed, will create new project during deployment');
    }

    // Set critical environment variables
    await this.setVercelEnvironmentVariables();
  }

  async setVercelEnvironmentVariables() {
    log('   Setting critical environment variables...');

    const criticalEnvVars = [
      'PRISMA_GENERATE_DATAPROXY=true',
      `SERVER_TYPE=${this.serverType}`,
      'APP_ENV=production'
    ];

    for (const envVar of criticalEnvVars) {
      try {
        execCommand(`vercel env add ${envVar} production`, `Set ${envVar.split('=')[0]}`);
      } catch (error) {
        logWarning(`Failed to set ${envVar.split('=')[0]} - may already exist`);
      }
    }
  }

  async step6_Deploy() {
    logStep(6, 'Deploying to Vercel');

    // Commit current changes
    try {
      execCommand('git add .', 'Stage changes');
      execCommand('git commit -m "Automated deployment preparation"', 'Commit changes');
      logSuccess('Changes committed');
    } catch (error) {
      logWarning('No changes to commit or git not initialized');
    }

    // Deploy to Vercel
    try {
      const deployResult = execCommand('vercel --prod --yes', 'Deploy to production');

      // Extract deployment URL
      const urlMatch = deployResult.match(/https:\/\/[^\s]+/);
      if (urlMatch) {
        this.deploymentUrl = urlMatch[0];
        logSuccess(`Deployed to: ${this.deploymentUrl}`);
      }
    } catch (error) {
      logError('Deployment failed');
      throw error;
    }
  }

  async step7_Verify() {
    logStep(7, 'Verifying Deployment');

    if (!this.deploymentUrl) {
      logWarning('Deployment URL not found, skipping verification');
      return;
    }

    // Wait for deployment to be ready
    log('   Waiting for deployment to be ready...');
    await this.sleep(30000); // Wait 30 seconds

    // Test health endpoint
    try {
      const healthUrl = `${this.deploymentUrl}/api/health`;
      log(`   Testing health endpoint: ${healthUrl}`);

      const response = await this.fetchWithTimeout(healthUrl, 30000);
      if (response.ok) {
        const data = await response.json();
        logSuccess(`Health check passed: ${data.status}`);

        if (data.database === 'connected') {
          logSuccess('Database connection verified');
        } else {
          logWarning('Database connection issue detected');
        }
      } else {
        logWarning(`Health check returned status: ${response.status}`);
      }
    } catch (error) {
      logWarning(`Health check failed: ${error.message}`);
    }

    // Test authentication endpoint
    try {
      const authUrl = `${this.deploymentUrl}/auth/signin`;
      log(`   Testing authentication page: ${authUrl}`);

      const response = await this.fetchWithTimeout(authUrl, 15000);
      if (response.ok) {
        logSuccess('Authentication page accessible');
      } else {
        logWarning(`Authentication page returned status: ${response.status}`);
      }
    } catch (error) {
      logWarning(`Authentication page test failed: ${error.message}`);
    }

    // Display final results
    this.displayDeploymentSummary();
  }

  async fetchWithTimeout(url, timeout = 10000) {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);

    try {
      const response = await fetch(url, { signal: controller.signal });
      clearTimeout(timeoutId);
      return response;
    } catch (error) {
      clearTimeout(timeoutId);
      throw error;
    }
  }

  async sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  displayDeploymentSummary() {
    log('\n📊 DEPLOYMENT SUMMARY', 'cyan');
    log('====================', 'cyan');

    if (this.deploymentUrl) {
      logSuccess(`🌐 Application URL: ${this.deploymentUrl}`);
      logSuccess(`🔐 Login Page: ${this.deploymentUrl}/auth/signin`);
      logSuccess(`❤️  Health Check: ${this.deploymentUrl}/api/health`);
    }

    log('\n📋 NEXT STEPS:', 'yellow');
    log('1. Set remaining environment variables in Vercel Dashboard');
    log('2. Configure DATABASE_URL with your production database');
    log('3. Set NEXTAUTH_SECRET (32+ characters)');
    log('4. Configure NEXTAUTH_URL with your actual domain');
    log('5. Set up inter-server communication variables');
    log('6. Test all functionality');

    log('\n🔧 ENVIRONMENT VARIABLES TO SET:', 'yellow');
    log('- DATABASE_URL (your production database)');
    log('- NEXTAUTH_SECRET (generate a secure 32+ char string)');
    log(`- NEXTAUTH_URL (${this.deploymentUrl || 'your-deployment-url'})`);
    log('- ADMIN_SERVER_URL');
    log('- STAFF_SERVER_URL');
    log('- INTER_SERVER_SECRET');
  }
}

// Run the deployment
if (require.main === module) {
  const deployer = new AutoDeployer();
  deployer.deploy().catch(error => {
    logError(`Deployment failed: ${error.message}`);
    process.exit(1);
  });
}

module.exports = AutoDeployer;

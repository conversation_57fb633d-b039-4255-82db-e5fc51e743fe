#!/usr/bin/env node

/**
 * Clean Deployment Script
 * 
 * Removes old deployments and cleans up for fresh deployment
 */

const fs = require('fs');
const { execSync } = require('child_process');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'white') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function execCommand(command, description) {
  try {
    log(`   Running: ${command}`, 'blue');
    const result = execSync(command, { encoding: 'utf8', stdio: 'pipe' });
    logSuccess(`${description} completed`);
    return result;
  } catch (error) {
    logWarning(`${description} failed: ${error.message}`);
    return null;
  }
}

class DeploymentCleaner {
  constructor() {
    this.projectRoot = process.cwd();
  }

  async clean() {
    try {
      log('\n🧹 CLEANING DEPLOYMENT', 'cyan');
      log('======================', 'cyan');
      
      await this.cleanLocalFiles();
      await this.cleanVercelProject();
      await this.cleanDependencies();
      await this.resetConfiguration();
      
      log('\n✨ CLEANUP COMPLETED!', 'green');
      log('====================', 'green');
      
    } catch (error) {
      logError(`Cleanup failed: ${error.message}`);
      process.exit(1);
    }
  }

  async cleanLocalFiles() {
    log('\n📁 Cleaning Local Files', 'blue');
    
    const filesToClean = [
      '.next',
      'node_modules',
      'package-lock.json',
      '.env.production',
      'DEPLOYMENT_ENV_VARS.md'
    ];
    
    filesToClean.forEach(file => {
      if (fs.existsSync(file)) {
        try {
          if (fs.statSync(file).isDirectory()) {
            execCommand(`rm -rf ${file}`, `Remove ${file} directory`);
          } else {
            fs.unlinkSync(file);
            logSuccess(`Removed ${file}`);
          }
        } catch (error) {
          logWarning(`Failed to remove ${file}: ${error.message}`);
        }
      } else {
        log(`   ${file} not found (already clean)`);
      }
    });
  }

  async cleanVercelProject() {
    log('\n🚀 Cleaning Vercel Project', 'blue');
    
    // Check if logged in to Vercel
    try {
      execCommand('vercel whoami', 'Check Vercel authentication');
    } catch (error) {
      logWarning('Not logged in to Vercel, skipping Vercel cleanup');
      return;
    }
    
    // Remove Vercel project link
    if (fs.existsSync('.vercel')) {
      try {
        execCommand('rm -rf .vercel', 'Remove Vercel project link');
      } catch (error) {
        logWarning('Failed to remove .vercel directory');
      }
    }
    
    // List and optionally remove deployments
    try {
      const deployments = execCommand('vercel ls', 'List deployments');
      if (deployments && deployments.includes('inno-crm')) {
        log('\n   Found existing deployments:');
        log(deployments);
        
        // Note: We don't automatically remove deployments as they might be in use
        logWarning('Manual cleanup of old deployments may be needed');
        log('   Use: vercel rm <deployment-url> to remove specific deployments');
      }
    } catch (error) {
      log('   No existing deployments found or failed to list');
    }
  }

  async cleanDependencies() {
    log('\n📦 Cleaning Dependencies', 'blue');
    
    // Clear npm cache
    try {
      execCommand('npm cache clean --force', 'Clear npm cache');
    } catch (error) {
      logWarning('Failed to clear npm cache');
    }
    
    // Clear Prisma cache
    try {
      execCommand('npx prisma generate', 'Clear Prisma cache');
    } catch (error) {
      logWarning('Failed to clear Prisma cache');
    }
  }

  async resetConfiguration() {
    log('\n⚙️ Resetting Configuration', 'blue');
    
    // Reset vercel.json to basic configuration
    const basicVercelConfig = {
      "buildCommand": "prisma generate && next build",
      "installCommand": "npm install",
      "framework": "nextjs",
      "env": {
        "PRISMA_GENERATE_DATAPROXY": "true"
      },
      "build": {
        "env": {
          "PRISMA_GENERATE_DATAPROXY": "true"
        }
      }
    };
    
    fs.writeFileSync('vercel.json', JSON.stringify(basicVercelConfig, null, 2));
    logSuccess('Reset vercel.json to basic configuration');
    
    // Create fresh environment template
    const envTemplate = `# ENVIRONMENT VARIABLES TEMPLATE
# Copy these to Vercel Dashboard → Settings → Environment Variables

# Database Configuration
DATABASE_URL="your-production-database-url"
PRISMA_GENERATE_DATAPROXY="true"

# Authentication
NEXTAUTH_SECRET="your-32-character-secret-key-here"
NEXTAUTH_URL="https://your-app.vercel.app"

# Application Configuration
APP_NAME="Innovative Centre"
APP_URL="https://your-app.vercel.app"
APP_ENV="production"
SERVER_TYPE="staff"

# Inter-Server Communication
ADMIN_SERVER_URL="https://inno-crm-admin.vercel.app"
STAFF_SERVER_URL="https://inno-crm-staff.vercel.app"
INTER_SERVER_SECRET="your-inter-server-secret-key"
`;
    
    fs.writeFileSync('.env.template', envTemplate);
    logSuccess('Created fresh environment template');
  }

  displayCleanupSummary() {
    log('\n📊 CLEANUP SUMMARY', 'cyan');
    log('==================', 'cyan');
    
    log('\n✅ Cleaned:');
    log('   • Local build files (.next, node_modules)');
    log('   • Package lock file');
    log('   • Previous environment files');
    log('   • Vercel project link');
    log('   • npm and Prisma cache');
    
    log('\n📋 NEXT STEPS:', 'yellow');
    log('1. Run setup: npm run deploy:setup');
    log('2. Configure environment variables');
    log('3. Deploy: npm run deploy:auto');
    log('4. Verify: npm run verify:deployment');
    
    log('\n🔧 MANUAL CLEANUP (if needed):', 'yellow');
    log('• Remove old Vercel deployments: vercel rm <deployment-url>');
    log('• Clear browser cache and cookies');
    log('• Update DNS records if using custom domain');
  }
}

// Run cleanup
if (require.main === module) {
  const cleaner = new DeploymentCleaner();
  cleaner.clean().then(() => {
    cleaner.displayCleanupSummary();
  }).catch(error => {
    logError(`Cleanup failed: ${error.message}`);
    process.exit(1);
  });
}

module.exports = DeploymentCleaner;

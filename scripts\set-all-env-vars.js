#!/usr/bin/env node

/**
 * Set All Environment Variables in Vercel
 * 
 * This script reads the .env.production file and sets all variables in Vercel
 */

const fs = require('fs');
const { execSync } = require('child_process');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'white') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function execCommand(command, description) {
  try {
    log(`   Running: ${command}`, 'blue');
    execSync(command, { stdio: 'inherit' });
    logSuccess(`${description} completed`);
  } catch (error) {
    logError(`${description} failed: ${error.message}`);
    throw error;
  }
}

async function setAllEnvironmentVariables() {
  log('\n🔧 SETTING ALL ENVIRONMENT VARIABLES', 'magenta');
  log('=====================================', 'magenta');
  
  // Check if .env.production exists
  if (!fs.existsSync('.env.production')) {
    logError('.env.production file not found!');
    log('Please run: npm run deploy:setup first');
    process.exit(1);
  }
  
  // Read environment variables from .env.production
  const envContent = fs.readFileSync('.env.production', 'utf8');
  const envLines = envContent.split('\n');
  const envVars = {};
  
  envLines.forEach(line => {
    const trimmedLine = line.trim();
    if (trimmedLine && !trimmedLine.startsWith('#')) {
      const [key, ...valueParts] = trimmedLine.split('=');
      if (key && valueParts.length > 0) {
        const value = valueParts.join('=').replace(/^"(.*)"$/, '$1');
        envVars[key] = value;
      }
    }
  });
  
  log(`\n📋 Found ${Object.keys(envVars).length} environment variables to set:`);
  Object.keys(envVars).forEach(key => {
    const value = envVars[key];
    const displayValue = key.includes('SECRET') || key.includes('PASSWORD') 
      ? '***HIDDEN***' 
      : value.length > 50 
        ? value.substring(0, 50) + '...' 
        : value;
    log(`   ${key} = ${displayValue}`);
  });
  
  // Set each environment variable in Vercel
  log('\n🚀 Setting environment variables in Vercel...');
  
  let successCount = 0;
  let errorCount = 0;
  
  for (const [key, value] of Object.entries(envVars)) {
    try {
      // Check if variable already exists
      let exists = false;
      try {
        execSync(`vercel env ls | findstr "${key}"`, { stdio: 'pipe' });
        exists = true;
      } catch (error) {
        // Variable doesn't exist, which is fine
      }
      
      if (exists) {
        log(`   ${key} already exists, removing first...`);
        try {
          execSync(`vercel env rm ${key} production --yes`, { stdio: 'pipe' });
        } catch (error) {
          // Ignore removal errors
        }
      }
      
      // Set the environment variable
      const command = `vercel env add ${key} "${value}" production`;
      execSync(command, { stdio: 'pipe' });
      logSuccess(`Set ${key}`);
      successCount++;
      
    } catch (error) {
      logError(`Failed to set ${key}: ${error.message}`);
      errorCount++;
    }
  }
  
  // Summary
  log('\n📊 ENVIRONMENT VARIABLE SETUP SUMMARY', 'cyan');
  log('====================================', 'cyan');
  logSuccess(`Successfully set: ${successCount} variables`);
  
  if (errorCount > 0) {
    logError(`Failed to set: ${errorCount} variables`);
  }
  
  // Update NEXTAUTH_URL with actual deployment URL
  log('\n🔄 Updating NEXTAUTH_URL with actual deployment URL...');
  try {
    // Get the actual deployment URL
    const deploymentInfo = execSync('vercel ls --scope=team', { encoding: 'utf8' });
    const lines = deploymentInfo.split('\n');
    let actualUrl = null;
    
    for (const line of lines) {
      if (line.includes('inno-crm-admin') && line.includes('https://')) {
        const urlMatch = line.match(/https:\/\/[^\s]+/);
        if (urlMatch) {
          actualUrl = urlMatch[0];
          break;
        }
      }
    }
    
    if (actualUrl && actualUrl !== envVars.NEXTAUTH_URL) {
      log(`   Updating NEXTAUTH_URL from ${envVars.NEXTAUTH_URL} to ${actualUrl}`);
      
      // Remove old NEXTAUTH_URL
      try {
        execSync(`vercel env rm NEXTAUTH_URL production --yes`, { stdio: 'pipe' });
      } catch (error) {
        // Ignore removal errors
      }
      
      // Set new NEXTAUTH_URL
      execSync(`vercel env add NEXTAUTH_URL "${actualUrl}" production`, { stdio: 'pipe' });
      logSuccess(`Updated NEXTAUTH_URL to: ${actualUrl}`);
      
      // Also update APP_URL
      try {
        execSync(`vercel env rm APP_URL production --yes`, { stdio: 'pipe' });
      } catch (error) {
        // Ignore removal errors
      }
      execSync(`vercel env add APP_URL "${actualUrl}" production`, { stdio: 'pipe' });
      logSuccess(`Updated APP_URL to: ${actualUrl}`);
    }
    
  } catch (error) {
    logWarning('Could not automatically update NEXTAUTH_URL');
  }
  
  // Trigger a new deployment to pick up the environment variables
  log('\n🚀 Triggering new deployment to apply environment variables...');
  try {
    execCommand('vercel --prod --yes', 'Redeploy with environment variables');
    logSuccess('Deployment triggered successfully!');
  } catch (error) {
    logWarning('Failed to trigger automatic redeployment');
    log('Please manually redeploy in Vercel dashboard or run: vercel --prod');
  }
  
  log('\n🎉 ENVIRONMENT SETUP COMPLETED!', 'green');
  log('===============================', 'green');
  log('\n📋 Next steps:');
  log('1. Wait for the new deployment to complete');
  log('2. Test the health endpoint');
  log('3. Test the login page');
  log('4. Verify all functionality works');
  
  log('\n🔗 Your application URLs:');
  log(`• Health Check: ${envVars.NEXTAUTH_URL || 'https://your-app.vercel.app'}/api/health`);
  log(`• Login Page: ${envVars.NEXTAUTH_URL || 'https://your-app.vercel.app'}/auth/signin`);
  log(`• Admin Panel: ${envVars.NEXTAUTH_URL || 'https://your-app.vercel.app'}/dashboard`);
}

// Run the script
if (require.main === module) {
  setAllEnvironmentVariables().catch(error => {
    logError(`Environment setup failed: ${error.message}`);
    process.exit(1);
  });
}

module.exports = { setAllEnvironmentVariables };

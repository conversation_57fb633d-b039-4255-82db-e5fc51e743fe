#!/usr/bin/env node

/**
 * Test Login API
 * 
 * This script tests the NextAuth login API directly to debug authentication issues
 */

const https = require('https')

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
}

function log(message, color = 'white') {
  console.log(`${colors[color]}${message}${colors.reset}`)
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green')
}

function logError(message) {
  log(`❌ ${message}`, 'red')
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow')
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue')
}

async function testLoginAPI() {
  try {
    log('🔐 TESTING LOGIN API', 'cyan')
    log('====================', 'cyan')
    
    const baseUrl = 'https://inno-crm-admin-kdz8e29uk-farrukh-tajibaevs-projects.vercel.app'
    
    // Test credentials
    const credentials = {
      phone: '+998906006299',
      password: 'Parviz0106$'
    }
    
    logInfo(`Testing login for: ${credentials.phone}`)
    logInfo(`Base URL: ${baseUrl}`)
    
    // First, get CSRF token
    log('\n🔑 Step 1: Getting CSRF token...', 'magenta')
    
    const csrfResponse = await fetch(`${baseUrl}/api/auth/csrf`)
    const csrfData = await csrfResponse.json()
    
    if (!csrfResponse.ok) {
      logError(`Failed to get CSRF token: ${csrfResponse.status}`)
      console.log('Response:', csrfData)
      return
    }
    
    logSuccess(`CSRF token obtained: ${csrfData.csrfToken.substring(0, 20)}...`)
    
    // Test login
    log('\n🔐 Step 2: Testing login...', 'magenta')
    
    const loginData = {
      phone: credentials.phone,
      password: credentials.password,
      csrfToken: csrfData.csrfToken,
      callbackUrl: `${baseUrl}/dashboard`,
      json: true
    }
    
    const loginResponse = await fetch(`${baseUrl}/api/auth/callback/credentials`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Cookie': csrfResponse.headers.get('set-cookie') || ''
      },
      body: new URLSearchParams(loginData)
    })
    
    logInfo(`Login response status: ${loginResponse.status}`)
    logInfo(`Login response headers:`)
    for (const [key, value] of loginResponse.headers.entries()) {
      console.log(`  ${key}: ${value}`)
    }
    
    const loginResult = await loginResponse.text()
    console.log('\nLogin response body:')
    console.log(loginResult)
    
    if (loginResponse.ok) {
      logSuccess('Login API call successful!')
    } else {
      logError('Login API call failed!')
    }
    
    // Test health endpoint
    log('\n🏥 Step 3: Testing health endpoint...', 'magenta')
    
    const healthResponse = await fetch(`${baseUrl}/api/health`)
    const healthData = await healthResponse.json()
    
    logInfo(`Health response status: ${healthResponse.status}`)
    console.log('Health response:', healthData)
    
    if (healthResponse.ok) {
      logSuccess('Health endpoint working!')
    } else {
      logError('Health endpoint failed!')
    }
    
  } catch (error) {
    logError(`Test failed: ${error.message}`)
    console.error('Full error:', error)
  }
}

// Polyfill fetch for Node.js if needed
if (typeof fetch === 'undefined') {
  global.fetch = async (url, options = {}) => {
    return new Promise((resolve, reject) => {
      const urlObj = new URL(url)
      const isHttps = urlObj.protocol === 'https:'
      const lib = isHttps ? require('https') : require('http')
      
      const requestOptions = {
        hostname: urlObj.hostname,
        port: urlObj.port || (isHttps ? 443 : 80),
        path: urlObj.pathname + urlObj.search,
        method: options.method || 'GET',
        headers: options.headers || {}
      }
      
      const req = lib.request(requestOptions, (res) => {
        let data = ''
        res.on('data', chunk => data += chunk)
        res.on('end', () => {
          resolve({
            ok: res.statusCode >= 200 && res.statusCode < 300,
            status: res.statusCode,
            statusText: res.statusMessage,
            headers: new Map(Object.entries(res.headers)),
            text: () => Promise.resolve(data),
            json: () => Promise.resolve(JSON.parse(data))
          })
        })
      })
      
      req.on('error', reject)
      
      if (options.body) {
        req.write(options.body)
      }
      
      req.end()
    })
  }
}

// Run the test
if (require.main === module) {
  testLoginAPI()
}

module.exports = { testLoginAPI }

import { NextAuthOptions } from "next-auth"
import Cred<PERSON><PERSON><PERSON><PERSON><PERSON> from "next-auth/providers/credentials"
import { prisma } from "@/lib/prisma"
import bcrypt from "bcryptjs"

export const authOptions: NextAuthOptions = {
  providers: [
    CredentialsProvider({
      name: "credentials",
      credentials: {
        phone: { label: "Phone", type: "text" },
        password: { label: "Password", type: "password" }
      },
      async authorize(credentials) {
        if (!credentials?.phone || !credentials?.password) {
          return null
        }

        try {
          // First, try local database authentication
          const user = await prisma.user.findUnique({
            where: { phone: credentials.phone },
            select: {
              id: true,
              phone: true,
              name: true,
              email: true,
              role: true,
              password: true,
            },
          })

          if (user) {
            // Verify password
            const isPasswordValid = await bcrypt.compare(credentials.password, user.password)
            if (isPasswordValid) {
              // Check if user role is allowed on this server
              const serverType = process.env.SERVER_TYPE || 'admin'
              const allowedRoles = serverType === 'admin'
                ? ['ADMIN', 'CASHIER', 'MANAGER', 'TEACHER', 'RECEPTION', 'STUDENT', 'ACADEMIC_MANAGER']
                : ['MANAGER', 'TEACHER', 'RECEPTION', 'STUDENT', 'ACADEMIC_MANAGER']

              if (allowedRoles.includes(user.role)) {
                const { password: _, ...userWithoutPassword } = user
                return userWithoutPassword
              } else {
                console.error(`User role ${user.role} not allowed on ${serverType} server`)
                return null
              }
            }
          }

          // If local auth fails and this is staff server, try inter-server authentication
          if (process.env.SERVER_TYPE === 'staff' && process.env.ADMIN_SERVER_URL && process.env.INTER_SERVER_SECRET) {
            const adminServerUrl = process.env.ADMIN_SERVER_URL
            const response = await fetch(`${adminServerUrl}/api/auth/verify`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                phone: credentials.phone,
                password: credentials.password,
                serverKey: process.env.INTER_SERVER_SECRET,
              }),
            })

            if (response.ok) {
              const data = await response.json()
              if (data.success && data.user) {
                const allowedRoles = ['MANAGER', 'TEACHER', 'RECEPTION', 'STUDENT', 'ACADEMIC_MANAGER']
                if (allowedRoles.includes(data.user.role)) {
                  return {
                    id: data.user.id,
                    phone: data.user.phone,
                    name: data.user.name,
                    email: data.user.email,
                    role: data.user.role,
                  }
                }
              }
            }
          }

          return null
        } catch (error) {
          console.error('Authentication error:', error)
          return null
        }
      }
    })
  ],
  session: {
    strategy: "jwt"
  },
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.role = user.role || null
      }
      return token
    },
    async session({ session, token }) {
      if (token) {
        session.user.id = token.sub!
        session.user.role = (token.role as string) || null
      }
      return session
    }
  },
  pages: {
    signIn: "/auth/signin",
  }
}

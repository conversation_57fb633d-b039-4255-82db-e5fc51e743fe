#!/usr/bin/env node

/**
 * Deployment Validation Script
 * 
 * Validates all aspects of the deployment before and after deployment
 */

const fs = require('fs');
const path = require('path');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'white') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

class DeploymentValidator {
  constructor() {
    this.errors = [];
    this.warnings = [];
    this.serverType = process.env.SERVER_TYPE || 'staff';
  }

  validate() {
    log('\n🔍 DEPLOYMENT VALIDATION', 'cyan');
    log('========================', 'cyan');

    this.validateFiles();
    this.validatePackageJson();
    this.validateEnvironmentVariables();
    this.validatePrismaConfiguration();
    this.validateNextConfiguration();
    this.validateVercelConfiguration();
    this.validateAuthConfiguration();
    this.validateInterServerConfiguration();

    this.displayResults();
    
    if (this.errors.length > 0) {
      process.exit(1);
    }
  }

  validateFiles() {
    log('\n📁 Validating Required Files', 'blue');
    
    const requiredFiles = [
      'package.json',
      'next.config.js',
      'vercel.json',
      'prisma/schema.prisma',
      'lib/auth.ts',
      'lib/prisma.ts',
      'middleware.ts',
      'app/api/health/route.ts'
    ];

    requiredFiles.forEach(file => {
      if (fs.existsSync(file)) {
        logSuccess(`Found ${file}`);
      } else {
        this.errors.push(`Missing required file: ${file}`);
        logError(`Missing ${file}`);
      }
    });
  }

  validatePackageJson() {
    log('\n📦 Validating package.json', 'blue');
    
    try {
      const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
      
      // Check critical dependencies
      const criticalDeps = [
        '@prisma/client',
        'prisma',
        'next-auth',
        'bcryptjs',
        'zod',
        'next'
      ];

      criticalDeps.forEach(dep => {
        if (packageJson.dependencies[dep] || packageJson.devDependencies[dep]) {
          logSuccess(`${dep} is installed`);
        } else {
          this.errors.push(`Missing critical dependency: ${dep}`);
          logError(`Missing ${dep}`);
        }
      });

      // Check build script
      if (packageJson.scripts.build && packageJson.scripts.build.includes('prisma generate')) {
        logSuccess('Build script includes Prisma generation');
      } else {
        this.errors.push('Build script missing Prisma generation');
        logError('Build script missing Prisma generation');
      }

      // Check postinstall script
      if (packageJson.scripts.postinstall && packageJson.scripts.postinstall.includes('prisma generate')) {
        logSuccess('Postinstall script includes Prisma generation');
      } else {
        this.warnings.push('Postinstall script missing Prisma generation');
        logWarning('Postinstall script missing Prisma generation');
      }

    } catch (error) {
      this.errors.push(`Invalid package.json: ${error.message}`);
      logError(`Invalid package.json: ${error.message}`);
    }
  }

  validateEnvironmentVariables() {
    log('\n🌍 Validating Environment Variables', 'blue');
    
    const requiredVars = [
      'DATABASE_URL',
      'NEXTAUTH_SECRET',
      'NEXTAUTH_URL'
    ];

    const recommendedVars = [
      'PRISMA_GENERATE_DATAPROXY',
      'SERVER_TYPE',
      'APP_ENV',
      'ADMIN_SERVER_URL',
      'STAFF_SERVER_URL',
      'INTER_SERVER_SECRET'
    ];

    // Check required variables
    requiredVars.forEach(varName => {
      if (process.env[varName]) {
        logSuccess(`${varName} is set`);
        
        // Validate specific formats
        if (varName === 'DATABASE_URL') {
          if (process.env[varName].startsWith('postgresql://') || process.env[varName].startsWith('postgres://')) {
            logSuccess('DATABASE_URL format is valid');
          } else {
            this.warnings.push('DATABASE_URL format might be incorrect');
            logWarning('DATABASE_URL format might be incorrect');
          }
        }
        
        if (varName === 'NEXTAUTH_SECRET') {
          if (process.env[varName].length >= 32) {
            logSuccess('NEXTAUTH_SECRET length is adequate');
          } else {
            this.errors.push('NEXTAUTH_SECRET should be at least 32 characters');
            logError('NEXTAUTH_SECRET should be at least 32 characters');
          }
        }
        
        if (varName === 'NEXTAUTH_URL') {
          if (process.env[varName].startsWith('https://')) {
            logSuccess('NEXTAUTH_URL uses HTTPS');
          } else {
            this.warnings.push('NEXTAUTH_URL should use HTTPS in production');
            logWarning('NEXTAUTH_URL should use HTTPS in production');
          }
        }
      } else {
        this.errors.push(`Missing required environment variable: ${varName}`);
        logError(`Missing ${varName}`);
      }
    });

    // Check recommended variables
    recommendedVars.forEach(varName => {
      if (process.env[varName]) {
        logSuccess(`${varName} is set`);
      } else {
        this.warnings.push(`Recommended environment variable missing: ${varName}`);
        logWarning(`Missing ${varName}`);
      }
    });

    // Validate PRISMA_GENERATE_DATAPROXY
    if (process.env.PRISMA_GENERATE_DATAPROXY === 'true') {
      logSuccess('PRISMA_GENERATE_DATAPROXY is correctly set');
    } else {
      this.errors.push('PRISMA_GENERATE_DATAPROXY must be set to "true" for Vercel deployment');
      logError('PRISMA_GENERATE_DATAPROXY must be "true"');
    }
  }

  validatePrismaConfiguration() {
    log('\n🗄️ Validating Prisma Configuration', 'blue');
    
    try {
      const schema = fs.readFileSync('prisma/schema.prisma', 'utf8');
      
      if (schema.includes('provider = "postgresql"')) {
        logSuccess('Prisma configured for PostgreSQL');
      } else {
        this.warnings.push('Prisma might not be configured for PostgreSQL');
        logWarning('Prisma might not be configured for PostgreSQL');
      }

      if (schema.includes('relationMode = "prisma"')) {
        logSuccess('Prisma relation mode is set');
      } else {
        this.warnings.push('Consider setting relationMode = "prisma" for better compatibility');
        logWarning('Consider setting relationMode = "prisma"');
      }

    } catch (error) {
      this.errors.push(`Cannot read Prisma schema: ${error.message}`);
      logError(`Cannot read Prisma schema: ${error.message}`);
    }
  }

  validateNextConfiguration() {
    log('\n⚡ Validating Next.js Configuration', 'blue');
    
    try {
      const nextConfigContent = fs.readFileSync('next.config.js', 'utf8');
      
      if (nextConfigContent.includes('serverExternalPackages')) {
        logSuccess('Next.js configured with serverExternalPackages');
      } else {
        this.warnings.push('Consider adding serverExternalPackages for better compatibility');
        logWarning('Missing serverExternalPackages configuration');
      }

      if (nextConfigContent.includes('@prisma/client')) {
        logSuccess('Prisma client externalized in webpack config');
      } else {
        this.warnings.push('Consider externalizing @prisma/client in webpack config');
        logWarning('Prisma client not externalized');
      }

    } catch (error) {
      this.errors.push(`Cannot read Next.js config: ${error.message}`);
      logError(`Cannot read Next.js config: ${error.message}`);
    }
  }

  validateVercelConfiguration() {
    log('\n🚀 Validating Vercel Configuration', 'blue');
    
    try {
      const vercelConfig = JSON.parse(fs.readFileSync('vercel.json', 'utf8'));
      
      if (vercelConfig.buildCommand && vercelConfig.buildCommand.includes('prisma generate')) {
        logSuccess('Vercel build command includes Prisma generation');
      } else {
        this.errors.push('Vercel build command missing Prisma generation');
        logError('Vercel build command missing Prisma generation');
      }

      if (vercelConfig.env && vercelConfig.env.PRISMA_GENERATE_DATAPROXY === 'true') {
        logSuccess('Vercel config includes PRISMA_GENERATE_DATAPROXY');
      } else {
        this.errors.push('Vercel config missing PRISMA_GENERATE_DATAPROXY');
        logError('Vercel config missing PRISMA_GENERATE_DATAPROXY');
      }

      if (vercelConfig.functions && vercelConfig.functions['app/api/**/*.ts']) {
        logSuccess('Vercel function timeout configured');
      } else {
        this.warnings.push('Consider configuring function timeout in vercel.json');
        logWarning('Function timeout not configured');
      }

    } catch (error) {
      this.errors.push(`Cannot read Vercel config: ${error.message}`);
      logError(`Cannot read Vercel config: ${error.message}`);
    }
  }

  validateAuthConfiguration() {
    log('\n🔐 Validating Authentication Configuration', 'blue');
    
    try {
      const authContent = fs.readFileSync('lib/auth.ts', 'utf8');
      
      if (authContent.includes('CredentialsProvider')) {
        logSuccess('Credentials provider configured');
      } else {
        this.errors.push('Credentials provider not found in auth configuration');
        logError('Credentials provider not configured');
      }

      if (authContent.includes('bcrypt')) {
        logSuccess('Password hashing configured');
      } else {
        this.warnings.push('Password hashing might not be configured');
        logWarning('Password hashing not found');
      }

      if (authContent.includes('strategy: "jwt"')) {
        logSuccess('JWT session strategy configured');
      } else {
        this.warnings.push('JWT session strategy might not be configured');
        logWarning('JWT session strategy not found');
      }

    } catch (error) {
      this.errors.push(`Cannot read auth configuration: ${error.message}`);
      logError(`Cannot read auth configuration: ${error.message}`);
    }
  }

  validateInterServerConfiguration() {
    log('\n🔗 Validating Inter-Server Configuration', 'blue');
    
    const serverType = process.env.SERVER_TYPE || 'staff';
    
    if (serverType === 'staff') {
      // Staff server should authenticate via admin server
      try {
        const authContent = fs.readFileSync('lib/auth.ts', 'utf8');
        
        if (authContent.includes('StaffServerAPI.authenticateUser')) {
          logSuccess('Staff server configured for inter-server authentication');
        } else {
          this.errors.push('Staff server not configured for inter-server authentication');
          logError('Inter-server authentication not configured');
        }
      } catch (error) {
        this.errors.push(`Cannot validate inter-server configuration: ${error.message}`);
        logError(`Cannot validate inter-server configuration: ${error.message}`);
      }
    }

    // Check inter-server library
    if (fs.existsSync('lib/inter-server.ts')) {
      logSuccess('Inter-server library found');
    } else {
      this.errors.push('Inter-server library missing');
      logError('Inter-server library missing');
    }
  }

  displayResults() {
    log('\n📊 VALIDATION RESULTS', 'cyan');
    log('===================', 'cyan');
    
    if (this.errors.length === 0 && this.warnings.length === 0) {
      logSuccess('🎉 All validations passed! Ready for deployment.');
    } else {
      if (this.errors.length > 0) {
        log(`\n❌ ERRORS (${this.errors.length}):`, 'red');
        this.errors.forEach(error => log(`   • ${error}`, 'red'));
      }
      
      if (this.warnings.length > 0) {
        log(`\n⚠️  WARNINGS (${this.warnings.length}):`, 'yellow');
        this.warnings.forEach(warning => log(`   • ${warning}`, 'yellow'));
      }
      
      if (this.errors.length > 0) {
        log('\n🚫 Deployment not recommended. Please fix errors first.', 'red');
      } else {
        log('\n✅ Deployment can proceed, but consider addressing warnings.', 'yellow');
      }
    }
  }
}

// Run validation
if (require.main === module) {
  const validator = new DeploymentValidator();
  validator.validate();
}

module.exports = DeploymentValidator;

require('dotenv').config({ path: '.env.local' })
const { PrismaClient } = require('@prisma/client')
const bcrypt = require('bcryptjs')

const prisma = new PrismaClient()

async function checkPasswordHash() {
  try {
    console.log('🔍 Checking password hash for user +998906006299...\n')
    
    const user = await prisma.user.findUnique({
      where: { phone: '+998906006299' },
      select: {
        id: true,
        phone: true,
        name: true,
        role: true,
        password: true,
        createdAt: true,
      }
    })

    if (!user) {
      console.log('❌ User not found')
      return
    }

    console.log('✅ User found:')
    console.log(`   Name: ${user.name}`)
    console.log(`   Phone: ${user.phone}`)
    console.log(`   Role: ${user.role}`)
    console.log(`   Created: ${user.createdAt}`)
    console.log(`   Password hash: ${user.password.substring(0, 20)}...`)
    console.log('')

    // Test password verification
    const testPassword = 'Parviz0106$'
    console.log(`🔐 Testing password: "${testPassword}"`)
    
    const isValid = await bcrypt.compare(testPassword, user.password)
    console.log(`   Password match: ${isValid ? '✅ YES' : '❌ NO'}`)
    
    if (!isValid) {
      console.log('\n🔧 Let\'s try to create a new hash for comparison:')
      const newHash = await bcrypt.hash(testPassword, 12)
      console.log(`   New hash: ${newHash}`)
      console.log(`   Original: ${user.password}`)
      console.log(`   Hashes match: ${newHash === user.password ? 'YES' : 'NO'}`)
    }

  } catch (error) {
    console.error('❌ Error:', error.message)
  } finally {
    await prisma.$disconnect()
  }
}

checkPasswordHash()

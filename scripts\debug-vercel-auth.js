// Debug script to test authentication on Vercel deployment
const testCredentials = {
  phone: '+998906006299',
  password: 'Parviz0106$'
}

async function testVercelAuth() {
  const vercelUrl = 'https://inno-crm-admin.vercel.app'
  
  console.log('🔍 Testing Vercel Authentication...\n')
  
  try {
    // Test 1: Check if the API endpoint exists
    console.log('1️⃣ Testing API endpoint availability...')
    const apiResponse = await fetch(`${vercelUrl}/api/auth/signin`, {
      method: 'GET'
    })
    console.log(`   Status: ${apiResponse.status}`)
    console.log(`   API endpoint accessible: ${apiResponse.ok ? '✅' : '❌'}\n`)

    // Test 2: Test authentication
    console.log('2️⃣ Testing authentication with working credentials...')
    const authResponse = await fetch(`${vercelUrl}/api/auth/callback/credentials`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        phone: testCredentials.phone,
        password: testCredentials.password,
        csrfToken: 'test', // This might need to be obtained first
        callbackUrl: `${vercelUrl}/dashboard`,
        json: 'true'
      })
    })
    
    console.log(`   Auth Status: ${authResponse.status}`)
    const authResult = await authResponse.text()
    console.log(`   Auth Response: ${authResult.substring(0, 200)}...\n`)

    // Test 3: Check environment variables via a custom endpoint
    console.log('3️⃣ Testing environment variables...')
    const envResponse = await fetch(`${vercelUrl}/api/debug/env`, {
      method: 'GET'
    })
    console.log(`   Env check status: ${envResponse.status}`)
    if (envResponse.ok) {
      const envData = await envResponse.text()
      console.log(`   Environment data: ${envData.substring(0, 200)}...\n`)
    }

  } catch (error) {
    console.error('❌ Error testing Vercel auth:', error.message)
  }
}

// Run the test
testVercelAuth()

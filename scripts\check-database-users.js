#!/usr/bin/env node

/**
 * Check Database Users and Create Admin if Missing
 *
 * This script checks the current state of users in the database
 * and creates the admin user if it doesn't exist
 */

// Load environment variables from .env.production
require('dotenv').config({ path: '.env.production' })

const { PrismaClient } = require('@prisma/client')
const bcrypt = require('bcryptjs')

const prisma = new PrismaClient()

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
}

function log(message, color = 'white') {
  console.log(`${colors[color]}${message}${colors.reset}`)
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green')
}

function logError(message) {
  log(`❌ ${message}`, 'red')
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow')
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue')
}

async function checkDatabaseConnection() {
  try {
    await prisma.$connect()
    logSuccess('Database connection successful')
    return true
  } catch (error) {
    logError(`Database connection failed: ${error.message}`)
    return false
  }
}

async function checkExistingUsers() {
  try {
    log('\n🔍 CHECKING EXISTING USERS', 'magenta')
    log('==========================', 'magenta')
    
    const users = await prisma.user.findMany({
      select: {
        id: true,
        phone: true,
        name: true,
        email: true,
        role: true,
        createdAt: true
      },
      orderBy: {
        createdAt: 'desc'
      }
    })
    
    if (users.length === 0) {
      logWarning('No users found in database')
      return []
    }
    
    logSuccess(`Found ${users.length} users in database:`)
    console.log('')
    
    users.forEach((user, index) => {
      log(`${index + 1}. ${user.name}`)
      log(`   Phone: ${user.phone}`)
      log(`   Email: ${user.email || 'N/A'}`)
      log(`   Role: ${user.role}`)
      log(`   Created: ${user.createdAt.toISOString()}`)
      console.log('')
    })
    
    return users
    
  } catch (error) {
    logError(`Error checking users: ${error.message}`)
    return []
  }
}

async function checkSpecificUser(phone) {
  try {
    const user = await prisma.user.findUnique({
      where: { phone },
      select: {
        id: true,
        phone: true,
        name: true,
        email: true,
        role: true,
        password: true,
        createdAt: true
      }
    })
    
    if (user) {
      logSuccess(`User ${phone} found:`)
      log(`   Name: ${user.name}`)
      log(`   Email: ${user.email || 'N/A'}`)
      log(`   Role: ${user.role}`)
      log(`   Password Hash: ${user.password.substring(0, 20)}...`)
      log(`   Created: ${user.createdAt.toISOString()}`)
      return user
    } else {
      logWarning(`User ${phone} not found`)
      return null
    }
    
  } catch (error) {
    logError(`Error checking user ${phone}: ${error.message}`)
    return null
  }
}

async function createAdminUser() {
  try {
    log('\n🔧 CREATING ADMIN USER', 'magenta')
    log('======================', 'magenta')
    
    const adminPhone = '+998906006299'
    const adminPassword = 'Parviz0106$'
    
    // Check if admin already exists
    const existingAdmin = await prisma.user.findUnique({
      where: { phone: adminPhone }
    })
    
    if (existingAdmin) {
      logWarning('Admin user already exists')
      return existingAdmin
    }
    
    // Hash password
    const hashedPassword = await bcrypt.hash(adminPassword, 12)
    
    // Create admin user
    const adminUser = await prisma.user.create({
      data: {
        phone: adminPhone,
        name: 'Parviz Adashov',
        email: '<EMAIL>',
        role: 'ADMIN',
        password: hashedPassword
      }
    })
    
    logSuccess('Admin user created successfully!')
    log(`   Phone: ${adminUser.phone}`)
    log(`   Password: ${adminPassword}`)
    log(`   Name: ${adminUser.name}`)
    log(`   Email: ${adminUser.email}`)
    log(`   Role: ${adminUser.role}`)
    
    return adminUser
    
  } catch (error) {
    logError(`Error creating admin user: ${error.message}`)
    return null
  }
}

async function testPasswordHash(phone, password) {
  try {
    log(`\n🔐 TESTING PASSWORD FOR ${phone}`, 'magenta')
    log('================================', 'magenta')
    
    const user = await prisma.user.findUnique({
      where: { phone },
      select: {
        password: true
      }
    })
    
    if (!user) {
      logError('User not found')
      return false
    }
    
    const isValid = await bcrypt.compare(password, user.password)
    
    if (isValid) {
      logSuccess('Password is correct!')
    } else {
      logError('Password is incorrect!')
    }
    
    return isValid
    
  } catch (error) {
    logError(`Error testing password: ${error.message}`)
    return false
  }
}

async function createTestUsers() {
  try {
    log('\n👥 CREATING TEST USERS', 'magenta')
    log('======================', 'magenta')
    
    const testUsers = [
      {
        phone: '+998901234568',
        name: 'Manager User',
        email: '<EMAIL>',
        role: 'MANAGER',
        password: 'manager123'
      },
      {
        phone: '+998912345678',
        name: 'Arevik',
        email: '<EMAIL>',
        role: 'RECEPTION',
        password: 'Arevik0106$'
      },
      {
        phone: '+998901234569',
        name: 'Reception User',
        email: '<EMAIL>',
        role: 'RECEPTION',
        password: 'reception123'
      }
    ]
    
    for (const userData of testUsers) {
      const existing = await prisma.user.findUnique({
        where: { phone: userData.phone }
      })
      
      if (existing) {
        log(`   User ${userData.phone} already exists`)
        continue
      }
      
      const hashedPassword = await bcrypt.hash(userData.password, 12)
      
      const user = await prisma.user.create({
        data: {
          phone: userData.phone,
          name: userData.name,
          email: userData.email,
          role: userData.role,
          password: hashedPassword
        }
      })
      
      logSuccess(`Created user: ${user.name} (${user.phone})`)
    }
    
  } catch (error) {
    logError(`Error creating test users: ${error.message}`)
  }
}

async function main() {
  try {
    log('🔍 DATABASE USER CHECKER', 'cyan')
    log('========================', 'cyan')
    
    // Check database connection
    const connected = await checkDatabaseConnection()
    if (!connected) {
      process.exit(1)
    }
    
    // Check existing users
    const users = await checkExistingUsers()
    
    // Check specific admin user
    log('\n🔍 CHECKING ADMIN USER (+998906006299)', 'magenta')
    log('======================================', 'magenta')
    const adminUser = await checkSpecificUser('+998906006299')
    
    if (!adminUser) {
      // Create admin user if it doesn't exist
      await createAdminUser()
    } else {
      // Test password
      await testPasswordHash('+998906006299', 'Parviz0106$')
    }
    
    // Create other test users if database is empty
    if (users.length < 3) {
      await createTestUsers()
    }
    
    // Final summary
    log('\n📋 SUMMARY', 'cyan')
    log('==========', 'cyan')
    const finalUsers = await prisma.user.count()
    logSuccess(`Total users in database: ${finalUsers}`)
    
    log('\n🔑 LOGIN CREDENTIALS:', 'green')
    log('Admin: +998906006299 / Parviz0106$')
    log('Manager: +998901234568 / manager123')
    log('Arevik: +998912345678 / Arevik0106$')
    log('Reception: +998901234569 / reception123')
    
  } catch (error) {
    logError(`Script failed: ${error.message}`)
    process.exit(1)
  } finally {
    await prisma.$disconnect()
  }
}

// Run the script
if (require.main === module) {
  main()
}

module.exports = { checkDatabaseConnection, checkExistingUsers, createAdminUser }

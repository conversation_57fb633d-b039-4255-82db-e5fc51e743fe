#!/usr/bin/env node

/**
 * Deployment Verification Script
 * Checks if the authentication system is properly configured
 */

const requiredEnvVars = {
  core: [
    'DATABASE_URL',
    'NEXTAUTH_SECRET',
    'NEXTAUTH_URL',
  ],
  server: [
    'SERVER_TYPE',
  ],
  interServer: [
    'INTER_SERVER_SECRET',
    'ADMIN_SERVER_URL',
    'STAFF_SERVER_URL',
  ]
}

function checkEnvironmentVariables() {
  console.log('🔍 Checking Environment Variables...\n')
  
  let allGood = true
  
  // Check core variables
  console.log('📋 Core Variables (Required):')
  requiredEnvVars.core.forEach(varName => {
    const value = process.env[varName]
    if (value) {
      const displayValue = varName === 'DATABASE_URL' ? '[HIDDEN]' : 
                          varName.includes('SECRET') ? '[HIDDEN]' : value
      console.log(`✅ ${varName}: ${displayValue}`)
    } else {
      console.log(`❌ ${varName}: Missing`)
      allGood = false
    }
  })
  
  // Check server type
  console.log('\n🖥️  Server Configuration:')
  requiredEnvVars.server.forEach(varName => {
    const value = process.env[varName]
    if (value) {
      console.log(`✅ ${varName}: ${value}`)
    } else {
      console.log(`⚠️  ${varName}: Not set (defaulting to 'admin')`)
    }
  })
  
  // Check inter-server variables
  console.log('\n🔗 Inter-Server Communication:')
  const serverType = process.env.SERVER_TYPE || 'admin'
  requiredEnvVars.interServer.forEach(varName => {
    const value = process.env[varName]
    if (value) {
      const displayValue = varName.includes('SECRET') ? '[HIDDEN]' : value
      console.log(`✅ ${varName}: ${displayValue}`)
    } else {
      if (serverType === 'staff' && varName === 'ADMIN_SERVER_URL') {
        console.log(`❌ ${varName}: Required for staff server`)
        allGood = false
      } else {
        console.log(`⚠️  ${varName}: Not set`)
      }
    }
  })
  
  return allGood
}

function validateConfiguration() {
  console.log('\n🔧 Configuration Validation...\n')
  
  const serverType = process.env.SERVER_TYPE || 'admin'
  const nextAuthUrl = process.env.NEXTAUTH_URL
  const adminServerUrl = process.env.ADMIN_SERVER_URL
  const staffServerUrl = process.env.STAFF_SERVER_URL
  
  console.log(`Server Type: ${serverType}`)
  
  // Validate NEXTAUTH_URL format
  if (nextAuthUrl) {
    if (nextAuthUrl.startsWith('http://localhost') || nextAuthUrl.startsWith('https://')) {
      console.log('✅ NEXTAUTH_URL format is valid')
    } else {
      console.log('❌ NEXTAUTH_URL should start with http:// or https://')
    }
  }
  
  // Validate server URLs for production
  if (process.env.NODE_ENV === 'production' || nextAuthUrl?.includes('vercel.app')) {
    if (adminServerUrl && !adminServerUrl.startsWith('https://')) {
      console.log('❌ ADMIN_SERVER_URL should use HTTPS in production')
    }
    if (staffServerUrl && !staffServerUrl.startsWith('https://')) {
      console.log('❌ STAFF_SERVER_URL should use HTTPS in production')
    }
  }
  
  // Check NEXTAUTH_SECRET strength
  const nextAuthSecret = process.env.NEXTAUTH_SECRET
  if (nextAuthSecret) {
    if (nextAuthSecret.length >= 32) {
      console.log('✅ NEXTAUTH_SECRET length is adequate')
    } else {
      console.log('⚠️  NEXTAUTH_SECRET should be at least 32 characters')
    }
  }
}

function printDeploymentInstructions() {
  const serverType = process.env.SERVER_TYPE || 'admin'
  
  console.log('\n📋 Deployment Instructions:\n')
  
  if (serverType === 'admin') {
    console.log('🔧 Admin Server Setup:')
    console.log('1. Set SERVER_TYPE="admin" in Vercel environment variables')
    console.log('2. Ensure DATABASE_URL points to admin database')
    console.log('3. Set unique NEXTAUTH_SECRET for admin server')
    console.log('4. Set NEXTAUTH_URL to admin server URL')
    console.log('5. Optionally set INTER_SERVER_SECRET for communication')
  } else {
    console.log('🔧 Staff Server Setup:')
    console.log('1. Set SERVER_TYPE="staff" in Vercel environment variables')
    console.log('2. Ensure DATABASE_URL points to staff database')
    console.log('3. Set unique NEXTAUTH_SECRET for staff server')
    console.log('4. Set NEXTAUTH_URL to staff server URL')
    console.log('5. Set ADMIN_SERVER_URL for inter-server authentication')
    console.log('6. Set INTER_SERVER_SECRET (same as admin server)')
  }
  
  console.log('\n🚀 After setting environment variables:')
  console.log('1. Redeploy the application in Vercel')
  console.log('2. Run database migrations: npx prisma db push')
  console.log('3. Seed the database: npm run db:seed')
  console.log('4. Test authentication with default credentials')
}

function main() {
  console.log('🔍 CRM Authentication Deployment Verification\n')
  console.log('=' .repeat(50))
  
  const envVarsOk = checkEnvironmentVariables()
  validateConfiguration()
  
  console.log('\n' + '='.repeat(50))
  
  if (envVarsOk) {
    console.log('✅ Environment configuration looks good!')
    console.log('🚀 Ready for deployment')
  } else {
    console.log('❌ Environment configuration has issues')
    console.log('🔧 Please fix the missing variables before deployment')
  }
  
  printDeploymentInstructions()
  
  console.log('\n📚 For detailed instructions, see: AUTHENTICATION_FIX_GUIDE.md')
}

// Run verification
main()

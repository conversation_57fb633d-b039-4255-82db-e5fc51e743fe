# Deployment Environment Variables

## Required Environment Variables

- **SERVER_TYPE**: `admin`
- **APP_NAME**: `inno-crm-admin`
- **APP_ENV**: `production`
- **NODE_ENV**: `production`
- **PRISMA_GENERATE_DATAPROXY**: `true`
- **DATABASE_URL**: `[HIDDEN]`
- **NEXTAUTH_SECRET**: `[HIDDEN]`
- **NEXTAUTH_URL**: `https://inno-crm-admin.vercel.app`
- **INTER_SERVER_SECRET**: `[HIDDEN]`
- **ADMIN_SERVER_URL**: `https://inno-crm-admin.vercel.app`
- **STAFF_SERVER_URL**: `https://inno-crm-staff.vercel.app`
- **APP_URL**: `https://inno-crm-admin.vercel.app`

## Setup Instructions

### Option 1: Use Vercel CLI Script
```bash
chmod +x scripts/set-vercel-env.sh
./scripts/set-vercel-env.sh
```

### Option 2: Manual Setup in Vercel Dashboard
1. Go to your Vercel project dashboard
2. Navigate to Settings → Environment Variables
3. Add each variable listed above
4. Set the environment to "Production"

### Option 3: Use Vercel CLI Manually
```bash
vercel env add SERVER_TYPE "admin" production
vercel env add APP_NAME "inno-crm-admin" production
vercel env add APP_ENV "production" production
vercel env add NODE_ENV "production" production
vercel env add PRISMA_GENERATE_DATAPROXY "true" production
vercel env add DATABASE_URL "postgresql://crm_owner:<EMAIL>/crm?sslmode=require&channel_binding=require" production
vercel env add NEXTAUTH_SECRET "668168bb8df424d340e5cef01134286dce123964d89cdf356a94a0edb386665f" production
vercel env add NEXTAUTH_URL "https://inno-crm-admin.vercel.app" production
vercel env add INTER_SERVER_SECRET "4f4aadf2fa0b65f265bd74aa0cf77fa0ce145fe20dd3e2c7eed72acfc5ade3c1" production
vercel env add ADMIN_SERVER_URL "https://inno-crm-admin.vercel.app" production
vercel env add STAFF_SERVER_URL "https://inno-crm-staff.vercel.app" production
vercel env add APP_URL "https://inno-crm-admin.vercel.app" production
```

## Important Notes

- Keep your secrets secure and never commit them to version control
- The NEXTAUTH_URL should match your actual deployment URL
- Both servers should use the same INTER_SERVER_SECRET
- Database URL should include `sslmode=require` for production

# 🚀 Automated Deployment System

**One-command deployment for your Inno-CRM to Vercel!**

## ⚡ Quick Start

### Super Simple (Recommended)
```bash
npm run deploy
```
Follow the interactive prompts - that's it! 🎉

### One-Line Deployment
```bash
npm run deploy -- --auto
```

### Clean Deployment (Remove old deployment first)
```bash
npm run deploy -- --clean
```

## 📋 Available Commands

| Command | Description |
|---------|-------------|
| `npm run deploy` | Interactive deployment wizard |
| `npm run deploy -- --auto` | Fully automated deployment |
| `npm run deploy -- --clean` | Clean and deploy |
| `npm run deploy -- --setup` | Setup environment only |
| `npm run deploy -- --validate` | Validate configuration |
| `npm run deploy -- --help` | Show help |

### Individual Scripts
| Command | Description |
|---------|-------------|
| `npm run deploy:clean` | Clean previous deployment |
| `npm run deploy:setup` | Interactive environment setup |
| `npm run deploy:validate` | Validate configuration |
| `npm run deploy:auto` | Automated deployment |
| `npm run verify:deployment` | Verify deployment |

## 🎯 What It Does Automatically

✅ **Environment Setup**
- Detects server type (admin/staff)
- Generates secure secrets
- Configures database connection
- Sets up inter-server communication

✅ **Configuration Optimization**
- Optimizes Vercel configuration
- Fixes Next.js settings
- Configures Prisma for production
- Sets up CORS and security headers

✅ **Deployment Process**
- Validates all requirements
- Installs dependencies
- Builds application
- Deploys to Vercel
- Verifies deployment success

✅ **Error Prevention**
- Checks for common issues
- Validates environment variables
- Tests database connection
- Ensures proper configuration

## 🗄️ Database Requirements

You need a **PostgreSQL database** (Neon recommended):

1. Create account at [neon.tech](https://neon.tech)
2. Create new project
3. Copy connection string
4. Use it during setup

Example format:
```
postgresql://username:password@host:port/database?sslmode=require
```

## 🔐 What You'll Need

The setup script will ask for:

### Required
- **Database URL** (PostgreSQL connection string)
- **Deployment URL** (your Vercel app URL)

### Auto-Generated
- **NEXTAUTH_SECRET** (32+ character secret)
- **INTER_SERVER_SECRET** (for server communication)

### Optional
- **SMS API Key** (for SMS functionality)
- **Email credentials** (for email functionality)

## 🌐 Dual-Server Deployment

### Admin Server
```bash
export SERVER_TYPE=admin
npm run deploy
```

### Staff Server
```bash
export SERVER_TYPE=staff
npm run deploy
```

Both servers will automatically:
- Share the same database
- Use secure inter-server communication
- Have proper role-based access control

## 🔍 Troubleshooting

### If Deployment Fails
```bash
# Check what's wrong
npm run deploy:validate

# Clean and try again
npm run deploy -- --clean
```

### Common Issues
- **Database connection**: Check your DATABASE_URL
- **Environment variables**: Run `npm run deploy:validate`
- **Build errors**: Check Node.js version (18+ required)
- **Vercel issues**: Ensure you're logged in: `vercel login`

### Debug Commands
```bash
# Check Vercel logs
vercel logs

# Test health endpoint
curl https://your-app.vercel.app/api/health

# Validate everything
npm run deploy:validate
```

## ✅ Verification

After deployment, the system automatically tests:
- ✅ Health endpoint responds
- ✅ Database connection works
- ✅ Authentication page loads
- ✅ Environment variables are set
- ✅ Inter-server communication (if staff server)

## 📊 Success Indicators

When deployment succeeds, you'll see:
```
🎉 DEPLOYMENT COMPLETED SUCCESSFULLY!
====================================

✅ Your CRM has been deployed to Vercel!

📋 Next steps:
1. Check your Vercel dashboard for the deployment URL
2. Test the health endpoint: /api/health
3. Access the login page: /auth/signin
4. Verify all functionality works correctly
```

## 🆘 Need Help?

### Documentation
- **Detailed Guide**: [DEPLOYMENT_GUIDE.md](DEPLOYMENT_GUIDE.md)
- **Authentication**: [AUTHENTICATION_SYSTEM_DOCUMENTATION.md](AUTHENTICATION_SYSTEM_DOCUMENTATION.md)

### Quick Help
```bash
npm run deploy:help
```

### Support Commands
```bash
# Show deployment status
vercel ls

# Show environment variables
vercel env ls

# Show project info
vercel inspect
```

## 🎯 Typical Deployment URLs

After successful deployment:
- **Admin Server**: `https://inno-crm-admin.vercel.app`
- **Staff Server**: `https://inno-crm-staff.vercel.app`
- **Health Check**: `https://your-app.vercel.app/api/health`
- **Login Page**: `https://your-app.vercel.app/auth/signin`

## 🔄 Updating Your Deployment

### Code Changes
```bash
git add .
git commit -m "Update code"
vercel --prod
```

### Environment Changes
```bash
npm run deploy:setup  # Reconfigure environment
```

### Full Redeployment
```bash
npm run deploy -- --clean  # Clean and redeploy
```

---

**Ready to deploy? Just run `npm run deploy` and follow the prompts! 🚀**

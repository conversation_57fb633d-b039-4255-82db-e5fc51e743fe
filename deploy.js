#!/usr/bin/env node

/**
 * Master Deployment Script for Inno-CRM
 * 
 * This is the main entry point for automated deployment.
 * It orchestrates all deployment steps and provides a simple interface.
 */

const fs = require('fs');
const readline = require('readline');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'white') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

class MasterDeployer {
  constructor() {
    this.rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });
  }

  async question(prompt) {
    return new Promise((resolve) => {
      this.rl.question(prompt, resolve);
    });
  }

  async deploy() {
    try {
      this.displayWelcome();
      
      const deploymentType = await this.selectDeploymentType();
      
      switch (deploymentType) {
        case 'full':
          await this.fullAutomatedDeployment();
          break;
        case 'clean':
          await this.cleanAndDeploy();
          break;
        case 'setup':
          await this.setupOnly();
          break;
        case 'validate':
          await this.validateOnly();
          break;
        case 'manual':
          await this.manualGuidance();
          break;
        default:
          logError('Invalid deployment type selected');
          process.exit(1);
      }
      
    } catch (error) {
      logError(`Deployment failed: ${error.message}`);
      process.exit(1);
    } finally {
      this.rl.close();
    }
  }

  displayWelcome() {
    log('\n🚀 INNO-CRM DEPLOYMENT MASTER', 'magenta');
    log('===============================', 'magenta');
    log('Welcome to the automated deployment system!', 'cyan');
    log('This tool will help you deploy your CRM to Vercel.\n');
  }

  async selectDeploymentType() {
    log('📋 DEPLOYMENT OPTIONS:', 'cyan');
    log('1. 🎯 Full Automated Deployment (Recommended)');
    log('2. 🧹 Clean & Deploy (Remove old deployment first)');
    log('3. ⚙️  Setup Environment Only');
    log('4. ✅ Validate Configuration Only');
    log('5. 📖 Manual Step-by-Step Guidance');
    
    const choice = await this.question('\nSelect deployment option (1-5): ');
    
    switch (choice) {
      case '1':
        return 'full';
      case '2':
        return 'clean';
      case '3':
        return 'setup';
      case '4':
        return 'validate';
      case '5':
        return 'manual';
      default:
        logWarning('Invalid choice, defaulting to full automated deployment');
        return 'full';
    }
  }

  async fullAutomatedDeployment() {
    log('\n🎯 FULL AUTOMATED DEPLOYMENT', 'cyan');
    log('=============================', 'cyan');
    
    const steps = [
      { name: 'Environment Setup', script: 'deploy:setup' },
      { name: 'Configuration Validation', script: 'deploy:validate' },
      { name: 'Automated Deployment', script: 'deploy:auto' },
      { name: 'Deployment Verification', script: 'verify:deployment' }
    ];
    
    for (const step of steps) {
      log(`\n🔄 Running: ${step.name}`, 'blue');
      await this.runScript(step.script);
    }
    
    this.displaySuccessMessage();
  }

  async cleanAndDeploy() {
    log('\n🧹 CLEAN & DEPLOY', 'cyan');
    log('=================', 'cyan');
    
    const confirm = await this.question('⚠️  This will remove all previous deployment data. Continue? (y/n): ');
    
    if (confirm.toLowerCase() !== 'y') {
      log('Deployment cancelled.');
      return;
    }
    
    const steps = [
      { name: 'Clean Previous Deployment', script: 'deploy:clean' },
      { name: 'Environment Setup', script: 'deploy:setup' },
      { name: 'Configuration Validation', script: 'deploy:validate' },
      { name: 'Automated Deployment', script: 'deploy:auto' },
      { name: 'Deployment Verification', script: 'verify:deployment' }
    ];
    
    for (const step of steps) {
      log(`\n🔄 Running: ${step.name}`, 'blue');
      await this.runScript(step.script);
    }
    
    this.displaySuccessMessage();
  }

  async setupOnly() {
    log('\n⚙️ ENVIRONMENT SETUP ONLY', 'cyan');
    log('=========================', 'cyan');
    
    await this.runScript('deploy:setup');
    
    log('\n✅ Environment setup completed!', 'green');
    log('Next steps:');
    log('1. Review generated environment files');
    log('2. Run: npm run deploy:validate');
    log('3. Run: npm run deploy:auto');
  }

  async validateOnly() {
    log('\n✅ CONFIGURATION VALIDATION', 'cyan');
    log('===========================', 'cyan');
    
    await this.runScript('deploy:validate');
    
    log('\n📋 Validation completed!', 'green');
    log('If validation passed, you can run: npm run deploy:auto');
  }

  async manualGuidance() {
    log('\n📖 MANUAL DEPLOYMENT GUIDANCE', 'cyan');
    log('==============================', 'cyan');
    
    log('\n📋 Step-by-step manual deployment:');
    log('\n1. 🧹 Clean previous deployment (optional):');
    log('   npm run deploy:clean');
    
    log('\n2. ⚙️ Set up environment:');
    log('   npm run deploy:setup');
    
    log('\n3. ✅ Validate configuration:');
    log('   npm run deploy:validate');
    
    log('\n4. 🚀 Deploy to Vercel:');
    log('   npm run deploy:auto');
    log('   OR manually:');
    log('   chmod +x scripts/set-vercel-env.sh');
    log('   ./scripts/set-vercel-env.sh');
    log('   vercel --prod');
    
    log('\n5. 🔍 Verify deployment:');
    log('   npm run verify:deployment');
    
    log('\n📚 For detailed instructions, see: DEPLOYMENT_GUIDE.md');
    
    const runNow = await this.question('\nWould you like to start the automated process now? (y/n): ');
    
    if (runNow.toLowerCase() === 'y') {
      await this.fullAutomatedDeployment();
    }
  }

  async runScript(scriptName) {
    const { execSync } = require('child_process');
    
    try {
      log(`   Executing: npm run ${scriptName}`, 'blue');
      execSync(`npm run ${scriptName}`, { 
        stdio: 'inherit',
        cwd: process.cwd()
      });
      logSuccess(`${scriptName} completed successfully`);
    } catch (error) {
      logError(`${scriptName} failed: ${error.message}`);
      throw error;
    }
  }

  displaySuccessMessage() {
    log('\n🎉 DEPLOYMENT COMPLETED SUCCESSFULLY!', 'green');
    log('====================================', 'green');
    
    log('\n✅ Your CRM has been deployed to Vercel!');
    log('\n📋 Next steps:');
    log('1. Check your Vercel dashboard for the deployment URL');
    log('2. Test the health endpoint: /api/health');
    log('3. Access the login page: /auth/signin');
    log('4. Verify all functionality works correctly');
    
    log('\n🔧 If you encounter issues:');
    log('• Check Vercel deployment logs');
    log('• Run: npm run verify:deployment');
    log('• Review: DEPLOYMENT_GUIDE.md');
    
    log('\n🎯 Deployment URLs (typical):');
    log('• Admin Server: https://inno-crm-admin.vercel.app');
    log('• Staff Server: https://inno-crm-staff.vercel.app');
    
    log('\n📞 Support:');
    log('• Documentation: DEPLOYMENT_GUIDE.md');
    log('• Authentication docs: AUTHENTICATION_SYSTEM_DOCUMENTATION.md');
  }
}

// Handle command line arguments
const args = process.argv.slice(2);

if (args.includes('--help') || args.includes('-h')) {
  console.log(`
🚀 Inno-CRM Deployment Master

Usage:
  node deploy.js                 # Interactive deployment
  node deploy.js --auto          # Full automated deployment
  node deploy.js --clean         # Clean and deploy
  node deploy.js --setup         # Setup environment only
  node deploy.js --validate      # Validate configuration only
  node deploy.js --help          # Show this help

Scripts:
  npm run deploy:clean           # Clean previous deployment
  npm run deploy:setup           # Interactive environment setup
  npm run deploy:validate        # Validate configuration
  npm run deploy:auto            # Automated deployment
  npm run verify:deployment      # Verify deployment

For detailed instructions, see: DEPLOYMENT_GUIDE.md
`);
  process.exit(0);
}

// Run deployment
if (require.main === module) {
  const deployer = new MasterDeployer();
  
  if (args.includes('--auto')) {
    deployer.fullAutomatedDeployment().catch(error => {
      console.error('Deployment failed:', error.message);
      process.exit(1);
    }).finally(() => {
      deployer.rl.close();
    });
  } else if (args.includes('--clean')) {
    deployer.cleanAndDeploy().catch(error => {
      console.error('Deployment failed:', error.message);
      process.exit(1);
    }).finally(() => {
      deployer.rl.close();
    });
  } else if (args.includes('--setup')) {
    deployer.setupOnly().catch(error => {
      console.error('Setup failed:', error.message);
      process.exit(1);
    }).finally(() => {
      deployer.rl.close();
    });
  } else if (args.includes('--validate')) {
    deployer.validateOnly().catch(error => {
      console.error('Validation failed:', error.message);
      process.exit(1);
    }).finally(() => {
      deployer.rl.close();
    });
  } else {
    deployer.deploy().catch(error => {
      console.error('Deployment failed:', error.message);
      process.exit(1);
    });
  }
}

module.exports = MasterDeployer;

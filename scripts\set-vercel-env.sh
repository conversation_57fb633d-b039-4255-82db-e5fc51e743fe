#!/bin/bash
# Vercel Environment Variables Setup Script
# Run this script to set all environment variables in Vercel

echo "Setting up Vercel environment variables..."

vercel env add SERVER_TYPE "admin" production
vercel env add APP_NAME "inno-crm-admin" production
vercel env add APP_ENV "production" production
vercel env add NODE_ENV "production" production
vercel env add PRISMA_GENERATE_DATAPROXY "true" production
vercel env add DATABASE_URL "postgresql://crm_owner:<EMAIL>/crm?sslmode=require&channel_binding=require" production
vercel env add NEXTAUTH_SECRET "668168bb8df424d340e5cef01134286dce123964d89cdf356a94a0edb386665f" production
vercel env add NEXTAUTH_URL "https://inno-crm-admin.vercel.app" production
vercel env add INTER_SERVER_SECRET "4f4aadf2fa0b65f265bd74aa0cf77fa0ce145fe20dd3e2c7eed72acfc5ade3c1" production
vercel env add ADMIN_SERVER_URL "https://inno-crm-admin.vercel.app" production
vercel env add STAFF_SERVER_URL "https://inno-crm-staff.vercel.app" production
vercel env add APP_URL "https://inno-crm-admin.vercel.app" production

echo "Environment variables setup completed!"
echo "You can now deploy with: vercel --prod"
